# ---------------------------------------------------------------- #
# ---------------------- DDB  ------------------------------------ #
# ---------------------------------------------------------------- #
DDB_AWS_REGION="ap-southeast-1"
DDB_MAX_ATTEMPTS=2
DDB_ENDPOINT="http://localstack:4566"
DDB_ENABLE_LOGGING=true
DDB_TABLE_TRANSACTION_LOGS="isg-gpayo-local-transLogs"
DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX="StatusIndex"
DDB_TABLE_CHANNEL="isg-gpayo-local-channels"
DDB_TABLE_EVENT_LOGS="isg-gpayo-local-eventLogs"

#----------------------------------------------------#
#----------- CHANNEL CB CONFIGURATION ---------------#
#----------------------------------------------------#
CXS_CALLBACK_API_KEY=api_key

# ---------------------------------------------------------------- #
# -------------------- APP SERVER CONFIGS ------------------------ #
# ---------------------------------------------------------------- #
LOG_LEVEL=info
NODE_ENV=local
