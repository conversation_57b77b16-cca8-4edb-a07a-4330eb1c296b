require('module-alias/register');
const logger = require('@configs/logger');
const { getAll } = require('@services/SampleService');

async function main() {
  try {
    logger.info('[JOB_STARTED] Starting <description> job');

    const transactions = await getAll();

    logger.info(`[JOB_COMPLETED] Processed ${transactions.count} transactions`);
  } catch (error) {
    logger.error(`[JOB_ERROR] Error processing job: ${error.message}`);
    logger.error(error.stack);
    throw error;
  }
}

// Only run the main function if this file is being executed directly
if (require.main === module) {
  main()
    .then(() => {
      logger.info('[JOB_COMPLETE] Process completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`[JOB_FAILED] ${error.message}`);
      process.exit(1);
    });
}

// Export the main function for testing
module.exports = main;
