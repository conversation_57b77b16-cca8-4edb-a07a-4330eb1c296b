const {
    DDB_TABLE_TRANSACTION_LOGS,
    DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX
  } = require('@configs/env');
  const BaseRepository = require('./BaseRepository');
  class TransactionLogsRepository extends BaseRepository {
    static instance;
  
    constructor() {
      super(DDB_TABLE_TRANSACTION_LOGS);
      TransactionLogsRepository.instance = this;
    }
  
    static createInstance() {
      if (!TransactionLogsRepository.instance) {
        TransactionLogsRepository.instance = new TransactionLogsRepository();
      }
      return TransactionLogsRepository.instance;
    }
  
    async getAll(limit = 1000, lastEvaluatedKey = null) {
      const scanParams = {
        Limit: limit
      };
  
      if (lastEvaluatedKey) {
        scanParams.ExclusiveStartKey = lastEvaluatedKey;
      }
  
      const result = await this.scan(scanParams);
  
      return {
        items: result.Items || [],
        count: result.Count,
        lastEvaluatedKey: result.LastEvaluatedKey
      };
    }
  
    async getExpiredTransactions(
      { currentDateTime, status },
      limit = 1000,
      lastEvaluatedKey = null
    ) {
      const queryParams = {
        IndexName: DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX,
        KeyConditionExpression: '#status = :status',
        FilterExpression: 'transactionExpiry <= :now',
        ExpressionAttributeNames: {
          '#status': 'status'
        },
        ExpressionAttributeValues: {
          ':now': currentDateTime,
          ':status': status
        },
        Limit: limit
      };
  
      if (lastEvaluatedKey) {
        queryParams.ExclusiveStartKey = lastEvaluatedKey;
      }
  
      const result = await this.query(queryParams);
  
      return {
        items: result.Items || [],
        lastEvaluatedKey: result.LastEvaluatedKey
      };
    }
  }
  
  module.exports = TransactionLogsRepository.createInstance();
  