const { DDB_TABLE_SETTLEMENTS } = require('@configs/env');
const BaseRepository = require('./BaseRepository');

class FailedTransactionsRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DDB_TABLE_SETTLEMENTS);
    FailedTransactionsRepository.instance = this;
  }

  static getInstance() {
    if (!FailedTransactionsRepository.instance) {
      FailedTransactionsRepository.instance =
        new FailedTransactionsRepository();
    }
    return FailedTransactionsRepository.instance;
  }
}

module.exports = FailedTransactionsRepository.getInstance();
