const { existsSync, readFileSync } = require('fs');
const openpgp = require('openpgp');

const encryptPgp = async (fileStream) => {
  const path = process.env.CRYPT_KEY;
  // added `backticks` to prevent error with tabs
  const publicKeyArmored = existsSync(path)
    ? `${readFileSync(path).toString()}`
    : null;
  const publicKey = await openpgp.readKey({ armoredKey: publicKeyArmored });
  const message = await openpgp.createMessage({ binary: fileStream });

  const encrypted = await openpgp.encrypt({
    message,
    encryptionKeys: publicKey,
    config: { rejectPublicKeyAlgorithms: new Set(), minRSABits: 0 }
  });

  return encrypted;
};

module.exports = {
  encryptPgp
};
