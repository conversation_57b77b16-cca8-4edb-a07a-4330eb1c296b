const { PassThrough } = require('stream');

const postingRules = {
  transactionDate: {
    length: 8
  },
  dealerName: {
    length: 40
  },
  customerAccountNo: {
    length: 20
  },
  transactionAmount: {
    length: 13
  },
  depositAccountNumber: {
    length: 15
  },
  serviceNumber: {
    length: 20
  },
  bankCode: {
    length: 10
  },
  bankAccountCheckNumber: {
    length: 20
  },
  terminalNumber: {
    length: 15
  },
  receiptingReferenceNumber: {
    length: 25
  },
  debitCreditIndicator: {
    length: 1
  },
  cashCheckIndicator: {
    length: 1
  },
  paymentMode: {
    length: 2
  }
  // extraField: {
  //   length: 23,
  // },
  // expiryDate: {
  //   length: 8,
  // },
};

const createFalloutFileStream = (settlements) => {
  const fileStream = new PassThrough();

  settlements.forEach((s) => {
    fileStream.write(convertLine(s) + '\n');
  });
  fileStream.end();

  return fileStream;
};

const convertLine = (data) => {
  let line = '';

  for (const key in postingRules) {
    const length = postingRules[key].length;
    if (key === 'transactionAmount') {
      let parsedAmount = data[key].toString().split('.');
      const amount = parsedAmount[0];
      const decimal =
        parsedAmount[1] !== undefined
          ? parsedAmount[1].slice(0, 2).padEnd(2, '0')
          : '00';
      const finalAmount = amount + decimal;

      line += getValue(finalAmount, length, '0', 'start');
    } else {
      line += getValue(data[key], length, ' ');
    }
  }

  return line;
};

const getValue = (data, length, pad = ' ', padType = 'end') => {
  let line = '';

  if (data !== undefined) {
    line = data.toString().slice(0, length);
    if (padType === 'end') {
      line = line.padEnd(length, pad);
    } else {
      line = line.padStart(length, pad);
    }
  } else {
    line = ''.padEnd(length, pad);
  }

  return line;
};

module.exports = {
  create
};
