const { stringTypeDefault, stringType, booleanTypeDefault } = require('./util');

const dynamoDBConfigs = {
  DDB_ENABLE_LOGGING: booleanTypeDefault(true),
  DDB_ENDPOINT: stringType,
  DDB_TABLE_TRANSACTION_LOGS: stringType,
  DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX: stringType,
  DDB_TABLE_EVENT_LOGS: stringType,
  DDB_TABLE_CHANNEL: stringType,
  CXS_CALLBACK_API_KEY: stringType
};

const schema = {
  type: 'object',
  required: [
    'DDB_TABLE_TRANSACTION_LOGS',
    'DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX',
    'DDB_TABLE_CHANNEL'
  ],
  properties: {
    NODE_ENV: stringTypeDefault('local'),
    LOG_LEVEL: stringTypeDefault('info'),
    ...dynamoDBConfigs
  }
};

module.exports = schema;
